# 学校报名类型管理功能实现说明

## 功能概述
在学校管理界面中，去除了编辑弹窗中的"招生类别"字段，新增了"设置学校报名类型"按钮，点击时弹出单独的弹窗进行报名类型的设置和管理。

## 实现的功能
1. **操作按钮**: 在学校管理表格的操作列中新增"设置学校报名类型"按钮
2. **独立弹窗**: 点击按钮后弹出专门的报名类型设置弹窗
3. **数据回显**: 弹窗中自动显示当前学校已设置的报名类型
4. **表单验证**: 添加了报名类型的必填验证
5. **数据保存**: 支持保存修改后的报名类型设置

## 修改的文件
- `src/views/setting/schoolManage/index.vue` (学校管理页面)

## 主要修改内容

### 1. 操作列新增按钮
在学校管理表格的操作列中添加了新按钮：
```html
<el-link
    icon="el-icon-s-tools"
    type="success"
    :underline="false"
    style="margin-right: 10px"
    @click="setSchoolEnrollType(row)"
>设置学校报名类型</el-link>
```

### 2. 新增独立弹窗
创建了专门的报名类型设置弹窗：
```html
<el-dialog
  title="设置学校报名类型"
  :visible.sync="modal.enrollTypeManage"
  width="600px"
  :close-on-click-modal="false">
  <!-- 弹窗内容 -->
</el-dialog>
```

### 3. 数据结构修改
添加了相关的数据字段：
```javascript
// 弹窗状态
modal: {
  enrollTypeManage: false // 设置报名类型弹窗
}

// 报名类型表单数据
enrollTypeForm: {
  schoolId: null,
  enrollTypeList: []
}

// 验证规则
enrollTypeRules: {
  enrollTypeList: [
    {
      required: true,
      message: "请选择招生类别",
      trigger: "change",
    },
  ],
}
```

### 4. 移除原有字段
从学校编辑弹窗中移除了招生类别相关内容：
- 移除了编辑表单中的招生类别checkbox组
- 移除了addForm中的enrollTypeList字段
- 移除了相关的验证规则
- 移除了保存时的enrollType设置逻辑

### 5. 新增方法
添加了两个核心方法：

#### setSchoolEnrollType(row)
- 打开设置报名类型弹窗
- 设置当前学校信息
- 回显已有的报名类型数据

#### saveSchoolEnrollType()
- 验证表单数据
- 调用API保存报名类型设置
- 刷新表格数据

## 用户操作流程
1. 用户在学校管理列表中找到目标学校
2. 点击该学校行的"设置学校报名类型"按钮
3. 弹出设置弹窗，显示学校名称和当前报名类型
4. 用户修改报名类型选择
5. 点击"确认"按钮保存设置
6. 系统提示保存成功，弹窗关闭，表格数据刷新

## 技术特点
1. **功能分离**: 将报名类型设置从学校编辑中独立出来
2. **数据回显**: 自动显示当前学校的报名类型设置
3. **表单验证**: 确保用户必须选择至少一个报名类型
4. **错误处理**: 完善的错误捕获和用户提示
5. **Loading状态**: 保存过程中显示loading状态

## 界面优化
1. **按钮样式**: 使用success类型的绿色按钮，配合工具图标
2. **弹窗布局**: 600px宽度，适合报名类型选择界面
3. **信息展示**: 弹窗顶部显示当前操作的学校名称
4. **操作反馈**: 保存成功后显示提示信息

## 数据流程
1. **打开弹窗**: 从学校行数据中获取enrollType，转换为数组格式
2. **数据编辑**: 用户通过checkbox组选择报名类型
3. **数据保存**: 将选择的类型数组转换为逗号分隔的字符串
4. **API调用**: 使用uptSchool接口更新学校的enrollType字段

## 完成状态
✅ 操作按钮添加 - 已完成
✅ 独立弹窗创建 - 已完成
✅ 数据结构设计 - 已完成
✅ 表单验证 - 已完成
✅ 数据保存逻辑 - 已完成
✅ 原有字段移除 - 已完成
✅ 用户体验优化 - 已完成
