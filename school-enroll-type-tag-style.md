# 学校报名类型标签样式布局实现说明

## 功能概述
将学校报名类型设置弹窗改为使用标签(tag)样式布局，模仿提供的设计图样式，提供更直观的用户交互体验。

## 设计特点
1. **标签式选择**: 使用el-tag组件替代checkbox，提供更直观的选择体验
2. **视觉反馈**: 选中和未选中状态有明显的视觉区别
3. **交互优化**: 点击标签即可切换选择状态
4. **布局美观**: 采用卡片式布局，标签整齐排列

## 实现的功能
1. **标签切换**: 点击标签可以切换选中/未选中状态
2. **状态显示**: 选中的标签显示为蓝色，未选中为灰色
3. **悬浮效果**: 鼠标悬浮时标签有轻微上移和阴影效果
4. **验证逻辑**: 保存时验证至少选择一个报名类型

## 修改的文件
- `src/views/setting/schoolManage/index.vue` (学校管理页面)

## 主要修改内容

### 1. 弹窗布局重构
```html
<div class="enroll-type-container">
  <div class="school-info">
    <span class="label">学校名称</span>
    <span class="value">{{ currentSchool ? currentSchool.deptName : '' }}</span>
  </div>
  
  <div class="enroll-type-section">
    <span class="label">学校报名类型</span>
    <div class="tag-container">
      <el-tag
        v-for="item in enrollTypeAll"
        :key="item.value"
        :type="enrollTypeForm.enrollTypeList.includes(item.value) ? 'primary' : 'info'"
        :effect="enrollTypeForm.enrollTypeList.includes(item.value) ? 'dark' : 'plain'"
        class="enroll-tag"
        @click="toggleEnrollType(item.value)"
        >{{ item.name }}</el-tag>
    </div>
  </div>
</div>
```

### 2. 标签交互逻辑
```javascript
// 切换报名类型选择
toggleEnrollType(value) {
  const index = this.enrollTypeForm.enrollTypeList.indexOf(value);
  if (index > -1) {
    // 如果已选中，则移除
    this.enrollTypeForm.enrollTypeList.splice(index, 1);
  } else {
    // 如果未选中，则添加
    this.enrollTypeForm.enrollTypeList.push(value);
  }
}
```

### 3. 样式设计
```scss
.enroll-type-container {
  .school-info {
    display: flex;
    align-items: center;
    margin-bottom: 30px;
  }
  
  .enroll-type-section {
    display: flex;
    align-items: flex-start;
    
    .tag-container {
      flex: 1;
      display: flex;
      flex-wrap: wrap;
      gap: 12px;
      padding: 10px;
      border: 1px solid #DCDFE6;
      border-radius: 4px;
      background-color: #FAFAFA;
      min-height: 60px;
      
      .enroll-tag {
        cursor: pointer;
        transition: all 0.3s;
        
        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
      }
    }
  }
}
```

## 界面特性

### 1. 布局结构
- **学校信息区**: 显示当前操作的学校名称
- **标签选择区**: 包含所有可选的报名类型标签
- **操作按钮区**: 取消和确定按钮

### 2. 视觉效果
- **选中状态**: 蓝色背景(primary)，深色效果(dark)
- **未选中状态**: 灰色边框(info)，浅色效果(plain)
- **悬浮效果**: 轻微上移和阴影，提供交互反馈
- **容器样式**: 浅灰色背景，圆角边框，类似卡片效果

### 3. 交互体验
- **点击切换**: 直接点击标签即可切换选择状态
- **视觉反馈**: 状态变化有明显的颜色区别
- **验证提示**: 未选择任何类型时显示警告信息

## 技术实现

### 1. 动态样式绑定
使用Vue的动态属性绑定实现标签状态切换：
```html
:type="enrollTypeForm.enrollTypeList.includes(item.value) ? 'primary' : 'info'"
:effect="enrollTypeForm.enrollTypeList.includes(item.value) ? 'dark' : 'plain'"
```

### 2. 数组操作
使用数组的indexOf、splice、push方法实现选择状态的切换

### 3. CSS过渡效果
使用transition属性实现平滑的悬浮动画效果

## 用户体验改进
1. **直观操作**: 点击标签比勾选checkbox更直观
2. **视觉清晰**: 选中状态一目了然
3. **交互流畅**: 悬浮和点击效果提供良好的反馈
4. **布局美观**: 标签整齐排列，界面更加美观

## 完成状态
✅ 标签式布局 - 已完成
✅ 交互逻辑 - 已完成
✅ 样式设计 - 已完成
✅ 状态切换 - 已完成
✅ 验证逻辑 - 已完成
✅ 用户体验优化 - 已完成
